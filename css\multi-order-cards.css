/* 多订单卡片样式 - 直立卡片设计，新配色，移动端优化 */

/* 多订单面板基础样式 */
.multi-order-panel {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    overflow-y: auto;
    padding: 16px;
    box-sizing: border-box;
}

.multi-order-content {
    max-width: 1200px;
    margin: 0 auto;
    background: #ffffff;
    border-radius: 16px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

/* 批量控制面板 - 紧凑设计 */
.batch-control-panel {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 12px 16px;
}

.batch-control-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.batch-title {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 600;
    font-size: 16px;
}

.batch-icon {
    font-size: 18px;
}

/* 下拉菜单设计 */
.batch-dropdown {
    position: relative;
}

.batch-dropdown-btn {
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: white;
    padding: 6px 12px;
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    transition: all 0.2s ease;
}

.batch-dropdown-btn:hover {
    background: rgba(255, 255, 255, 0.3);
}

.dropdown-arrow {
    font-size: 12px;
    transition: transform 0.2s ease;
}

.batch-dropdown-content {
    position: absolute;
    top: 100%;
    right: 0;
    background: white;
    border-radius: 12px;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
    padding: 16px;
    min-width: 280px;
    z-index: 1001;
    display: none;
    color: #333;
}

.batch-option {
    margin-bottom: 16px;
    padding-bottom: 16px;
    border-bottom: 1px solid #f0f0f0;
}

.batch-option:last-child {
    border-bottom: none;
    margin-bottom: 0;
    padding-bottom: 0;
}

.batch-option-label {
    display: block;
    font-weight: 600;
    color: #333;
    margin-bottom: 8px;
    font-size: 14px;
}

.batch-option-select {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
    margin-bottom: 8px;
}

.batch-apply-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 6px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.batch-apply-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

/* 批量操作按钮区域 */
.batch-actions {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 8px;
    padding-top: 16px;
    border-top: 2px solid #f0f0f0;
}

.batch-action-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
    padding: 8px 12px;
    border: none;
    border-radius: 8px;
    font-size: 12px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.btn-create-all {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    grid-column: 1 / -1; /* 跨两列 */
}

.btn-select-all {
    background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
    color: white;
}

.btn-clear-all {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
    color: white;
}

.batch-action-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.batch-summary {
    text-align: center;
    font-size: 14px;
    font-weight: 500;
    opacity: 0.9;
}

/* 订单卡片网格 */
.multi-order-list {
    padding: 16px;
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 16px;
    background: #f8f9fa;
}

/* 直立卡片设计 */
.order-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    overflow: hidden;
    border: 2px solid transparent;
    position: relative;
}

.order-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.order-card.selected {
    border-color: #667eea;
    background: linear-gradient(135deg, #f8f9ff 0%, #e8eaff 100%);
}

.order-card.paging-order {
    border-left: 4px solid #ffc107;
}

/* 卡片头部 */
.order-card-header {
    padding: 12px 16px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.order-selector {
    display: flex;
    align-items: center;
    gap: 10px;
}

.order-checkbox {
    width: 18px;
    height: 18px;
    accent-color: white;
}

.order-title {
    display: flex;
    align-items: center;
    gap: 8px;
}

.order-number {
    font-weight: 600;
    font-size: 14px;
}

.paging-badge {
    background: rgba(255, 193, 7, 0.2);
    color: #ffc107;
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 10px;
    font-weight: 500;
}

.order-status {
    display: flex;
    align-items: center;
}

.status-badge {
    padding: 4px 8px;
    border-radius: 6px;
    font-size: 11px;
    font-weight: 500;
}

.status-ready {
    background: rgba(40, 167, 69, 0.2);
    color: #28a745;
}

/* 卡片主体 */
.order-card-body {
    padding: 16px;
    cursor: pointer;
}

.order-card-body:hover {
    background: #f8f9fa;
}

/* 卡片底部操作区 */
.order-card-footer {
    padding: 12px 16px;
    background: #f8f9fa;
    border-top: 1px solid #e9ecef;
    display: flex;
    justify-content: center;
}

.btn-card-action {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 8px 16px;
    border: none;
    border-radius: 8px;
    font-size: 13px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    min-height: 36px;
}

.btn-create {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    width: 100%;
}

.btn-create:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3);
}

.btn-icon {
    font-size: 16px;
}

.btn-text {
    font-weight: 600;
}

/* 移动端优化 */
@media (max-width: 768px) {
    .multi-order-panel {
        padding: 8px;
    }
    
    .batch-control-panel {
        padding: 8px 12px;
    }
    
    .batch-title {
        font-size: 14px;
    }
    
    .batch-dropdown-btn {
        padding: 4px 8px;
        font-size: 12px;
    }
    
    .batch-dropdown-content {
        min-width: 240px;
        padding: 12px;
    }
    
    .multi-order-list {
        grid-template-columns: 1fr;
        gap: 12px;
        padding: 12px;
    }
    
    .order-card-header {
        padding: 10px 12px;
    }
    
    .order-card-body {
        padding: 12px;
    }
    
    .order-card-footer {
        padding: 10px 12px;
    }
    
    .btn-card-action {
        padding: 6px 12px;
        font-size: 12px;
        min-height: 32px;
    }
    
    .batch-actions {
        grid-template-columns: 1fr;
        gap: 6px;
    }
    
    .btn-create-all {
        grid-column: 1;
    }
}

/* 超小屏幕优化 */
@media (max-width: 480px) {
    .multi-order-panel {
        padding: 4px;
    }
    
    .batch-control-panel {
        padding: 6px 8px;
    }
    
    .order-card-header {
        padding: 8px 10px;
    }
    
    .order-card-body {
        padding: 10px;
    }
    
    .order-card-footer {
        padding: 8px 10px;
    }
    
    .btn-card-action {
        padding: 4px 8px;
        font-size: 11px;
        min-height: 28px;
    }
    
    .batch-dropdown-content {
        min-width: 200px;
        padding: 8px;
    }
}

/* 动画效果 */
@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.order-card {
    animation: slideIn 0.3s ease-out;
}

.batch-dropdown-content {
    animation: slideIn 0.2s ease-out;
}

/* 关闭按钮样式 */
.multi-order-close-btn {
    position: absolute;
    top: 16px;
    right: 16px;
    background: rgba(0, 0, 0, 0.5);
    color: white;
    border: none;
    border-radius: 50%;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-size: 18px;
    z-index: 1002;
}

.multi-order-close-btn:hover {
    background: rgba(0, 0, 0, 0.7);
}