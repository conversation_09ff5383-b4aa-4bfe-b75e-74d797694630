<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>优化的头部按钮演示</title>
    <link rel="stylesheet" href="style.css">
    <link rel="stylesheet" href="css/multi-order-cards.css">
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: var(--bg-primary, #f5f5f5);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        
        .demo-container {
            max-width: 800px;
            margin: 0 auto;
            background: var(--bg-secondary, white);
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }
        
        .demo-section {
            margin-bottom: 40px;
            padding: 20px;
            border: 1px solid var(--border-color, #e0e0e0);
            border-radius: 8px;
            background: var(--bg-tertiary, #fafafa);
        }
        
        .demo-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 15px;
            color: var(--text-primary, #333);
        }
        
        .demo-description {
            font-size: 14px;
            color: var(--text-secondary, #666);
            margin-bottom: 20px;
            line-height: 1.5;
        }
        
        .status-display {
            margin-top: 15px;
            padding: 10px;
            border-radius: 6px;
            background: var(--bg-secondary, white);
            border: 1px solid var(--border-color, #e0e0e0);
            font-family: monospace;
            font-size: 12px;
            color: var(--text-secondary, #666);
        }
        
        .color-demo {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .color-section {
            text-align: center;
            padding: 15px;
            border-radius: 8px;
            background: var(--bg-secondary, white);
        }
        
        .color-label {
            font-size: 12px;
            font-weight: 600;
            margin-bottom: 10px;
            color: var(--text-primary, #333);
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <h1>头部按钮样式优化演示</h1>
        <p>这个页面展示了优化后的多订单面板头部按钮的样式和交互效果。</p>

        <div class="demo-section">
            <div class="demo-title">基础按钮组</div>
            <div class="demo-description">
                这是主要的按钮组合，包含批量创建、最小化/最大化和关闭按钮。
            </div>
            <div class="header-actions">
                <button type="button" id="batchCreateBtn" class="btn btn-primary btn-sm">批量创建</button>
                <button type="button" class="btn btn-icon btn-toggle" title="最小化/最大化" id="togglePanelSizeBtn">📐</button>
                <button type="button" id="closeMultiOrderBtn" class="btn btn-icon btn-close" title="关闭">✕</button>
            </div>
            <div class="status-display" id="statusDisplay">点击按钮查看交互效果...</div>
        </div>

        <div class="demo-section">
            <div class="demo-title">按钮状态演示</div>
            <div class="demo-description">
                演示不同状态下的按钮效果：正常、加载中、成功、错误和禁用状态。
            </div>
            <div class="header-actions">
                <button type="button" class="btn btn-primary btn-sm" id="normalBtn">正常状态</button>
                <button type="button" class="btn btn-primary btn-sm loading" id="loadingBtn">加载中</button>
                <button type="button" class="btn btn-primary btn-sm success" id="successBtn">成功状态</button>
                <button type="button" class="btn btn-primary btn-sm error" id="errorBtn">错误状态</button>
                <button type="button" class="btn btn-primary btn-sm" disabled id="disabledBtn">禁用状态</button>
            </div>
        </div>

        <div class="demo-section">
            <div class="demo-title">图标按钮集合</div>
            <div class="demo-description">
                不同类型的图标按钮，展示各种颜色主题和用途。
            </div>
            <div class="header-actions">
                <button type="button" class="btn btn-icon btn-toggle" title="切换">📐</button>
                <button type="button" class="btn btn-icon btn-close" title="关闭">✕</button>
                <button type="button" class="btn btn-icon" title="设置">⚙️</button>
                <button type="button" class="btn btn-icon" title="帮助">❓</button>
                <button type="button" class="btn btn-icon" title="刷新">🔄</button>
                <button type="button" class="btn btn-icon" title="下载">📥</button>
            </div>
        </div>

        <div class="demo-section">
            <div class="demo-title">移动端适配预览</div>
            <div class="demo-description">
                在较小屏幕上，按钮会自动调整大小和间距以提供更好的触摸体验。
                可以调整浏览器窗口大小来查看效果。
            </div>
            <div class="header-actions">
                <button type="button" class="btn btn-primary btn-sm">批量操作</button>
                <button type="button" class="btn btn-icon btn-toggle">📐</button>
                <button type="button" class="btn btn-icon btn-close">✕</button>
            </div>
        </div>

        <div class="demo-section">
            <div class="demo-title">颜色主题展示</div>
            <div class="demo-description">
                不同的颜色主题用于区分按钮的功能和重要性。
            </div>
            <div class="color-demo">
                <div class="color-section">
                    <div class="color-label">主要操作 (Primary)</div>
                    <button type="button" class="btn btn-primary btn-sm">批量创建</button>
                </div>
                <div class="color-section">
                    <div class="color-label">切换功能 (Toggle)</div>
                    <button type="button" class="btn btn-icon btn-toggle">📐</button>
                </div>
                <div class="color-section">
                    <div class="color-label">关闭操作 (Close)</div>
                    <button type="button" class="btn btn-icon btn-close">✕</button>
                </div>
                <div class="color-section">
                    <div class="color-label">成功状态 (Success)</div>
                    <button type="button" class="btn btn-primary btn-sm success">完成</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 演示按钮交互效果
        document.addEventListener('DOMContentLoaded', function() {
            const statusDisplay = document.getElementById('statusDisplay');
            
            // 为所有按钮添加点击事件
            document.querySelectorAll('.btn').forEach(button => {
                button.addEventListener('click', function(e) {
                    if (this.disabled) return;
                    
                    const buttonText = this.textContent || this.title || '按钮';
                    const timestamp = new Date().toLocaleTimeString();
                    statusDisplay.textContent = `[${timestamp}] 点击了：${buttonText}`;
                    
                    // 演示加载状态
                    if (this.id === 'normalBtn') {
                        this.classList.add('loading');
                        this.textContent = '';
                        setTimeout(() => {
                            this.classList.remove('loading');
                            this.classList.add('success');
                            this.textContent = '完成';
                            setTimeout(() => {
                                this.classList.remove('success');
                                this.textContent = '正常状态';
                            }, 2000);
                        }, 2000);
                    }
                    
                    // 添加点击波纹效果
                    const ripple = document.createElement('span');
                    ripple.classList.add('ripple');
                    this.appendChild(ripple);
                    setTimeout(() => ripple.remove(), 600);
                });
            });

            // 演示按钮状态切换
            const toggleBtn = document.getElementById('togglePanelSizeBtn');
            let isMinimized = false;
            
            if (toggleBtn) {
                toggleBtn.addEventListener('click', function() {
                    isMinimized = !isMinimized;
                    this.textContent = isMinimized ? '📄' : '📐';
                    this.title = isMinimized ? '最大化' : '最小化';
                    statusDisplay.textContent = `[${new Date().toLocaleTimeString()}] 面板${isMinimized ? '已最小化' : '已最大化'}`;
                });
            }

            // 模拟错误状态演示
            const errorBtn = document.getElementById('errorBtn');
            if (errorBtn) {
                errorBtn.addEventListener('click', function() {
                    this.classList.add('error');
                    setTimeout(() => {
                        this.classList.remove('error');
                    }, 1000);
                });
            }
        });
    </script>

    <style>
        /* 点击波纹效果 */
        .btn {
            position: relative;
            overflow: hidden;
        }
        
        .ripple {
            position: absolute;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.6);
            transform: scale(0);
            animation: ripple 600ms linear;
            pointer-events: none;
        }
        
        @keyframes ripple {
            to {
                transform: scale(4);
                opacity: 0;
            }
        }
    </style>
</body>
</html>
