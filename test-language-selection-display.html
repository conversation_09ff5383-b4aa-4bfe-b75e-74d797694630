<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>语言选择显示测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            line-height: 1.6;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 30px 0;
            padding: 20px;
            border: 2px solid #ddd;
            border-radius: 8px;
            background-color: #f9f9f9;
        }
        .test-input {
            width: 100%;
            height: 80px;
            margin: 10px 0;
            padding: 10px;
            border: 1px solid #ccc;
            border-radius: 5px;
            font-family: monospace;
        }
        .language-display {
            margin: 15px 0;
            padding: 15px;
            background-color: white;
            border: 1px solid #ccc;
            border-radius: 5px;
        }
        .language-item {
            margin: 8px 0;
            padding: 8px;
            border-radius: 3px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .selected {
            background-color: #e8f5e8;
            border-left: 4px solid #4CAF50;
        }
        .unselected {
            background-color: #f5f5f5;
            border-left: 4px solid #ccc;
        }
        .test-button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background-color: #0056b3;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-selected {
            background-color: #4CAF50;
        }
        .status-unselected {
            background-color: #ccc;
        }
        .log-output {
            background-color: #f0f0f0;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🗣️ 语言选择显示测试</h1>
        <p>测试中文自动检测和语言选择的视觉反馈显示</p>
        
        <div class="test-section">
            <h2>📝 输入测试</h2>
            <p>在下面的文本框中输入订单信息，系统会自动检测并选择语言：</p>
            
            <textarea class="test-input" id="testInput" placeholder="请输入订单信息进行测试...">团号：EJBTBY250715
2PAX
15/7 KLIA2 IN 2015 (AK181) - MOXY PUTRAJAYA
客人：顾婉婷 & 苟晓琼
客人联系：13884407028</textarea>
            
            <div>
                <button class="test-button" onclick="testChineseText()">🇨🇳 测试中文内容</button>
                <button class="test-button" onclick="testEnglishText()">🇺🇸 测试英文内容</button>
                <button class="test-button" onclick="testMixedText()">🌐 测试混合内容</button>
                <button class="test-button" onclick="clearInput()">🗑️ 清空输入</button>
            </div>
        </div>

        <div class="test-section">
            <h2>🎯 语言选择状态</h2>
            <div class="language-display" id="languageDisplay">
                <div class="language-item unselected" id="lang-2">
                    <span><span class="status-indicator status-unselected"></span>英文 (English)</span>
                    <span>ID: 2</span>
                </div>
                <div class="language-item unselected" id="lang-3">
                    <span><span class="status-indicator status-unselected"></span>马来文 (Malay)</span>
                    <span>ID: 3</span>
                </div>
                <div class="language-item unselected" id="lang-4">
                    <span><span class="status-indicator status-unselected"></span>中文 (Chinese)</span>
                    <span>ID: 4</span>
                </div>
                <div class="language-item unselected" id="lang-5">
                    <span><span class="status-indicator status-unselected"></span>举牌服务 (Paging)</span>
                    <span>ID: 5</span>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h2>📊 检测结果</h2>
            <div id="detectionResult">
                <p><strong>检测状态：</strong><span id="detectionStatus">等待输入...</span></p>
                <p><strong>推荐语言：</strong><span id="recommendedLanguage">未检测</span></p>
                <p><strong>置信度：</strong><span id="confidence">0%</span></p>
            </div>
        </div>

        <div class="test-section">
            <h2>🔍 日志输出</h2>
            <div class="log-output" id="logOutput">系统初始化中...</div>
            <button class="test-button" onclick="clearLog()">清除日志</button>
        </div>
    </div>

    <script>
        // 模拟中文检测器
        class TestChineseDetector {
            constructor() {
                this.chineseRegex = /[\u4e00-\u9fff]/;
                this.languages = [
                    { id: 2, name: '英文 (English)' },
                    { id: 3, name: '马来文 (Malay)' },
                    { id: 4, name: '中文 (Chinese)' },
                    { id: 5, name: '举牌服务 (Paging)' }
                ];
            }

            detectLanguage(text) {
                const hasChinese = this.chineseRegex.test(text);
                const recommendedId = hasChinese ? 4 : 2;
                const confidence = this.calculateConfidence(text, hasChinese);
                
                return {
                    hasChinese: hasChinese,
                    recommendedLanguageId: recommendedId,
                    languageName: this.getLanguageName(recommendedId),
                    confidence: confidence
                };
            }

            calculateConfidence(text, hasChinese) {
                if (!text.trim()) return 0;
                
                // 简单的置信度计算
                const chineseCount = (text.match(/[\u4e00-\u9fff]/g) || []).length;
                const totalChars = text.replace(/\s/g, '').length;
                
                if (hasChinese) {
                    return Math.min(95, 70 + (chineseCount / totalChars * 25));
                } else {
                    const englishCount = (text.match(/[a-zA-Z]/g) || []).length;
                    return Math.min(95, 60 + (englishCount / totalChars * 35));
                }
            }

            getLanguageName(id) {
                const lang = this.languages.find(l => l.id === id);
                return lang ? lang.name : '未知语言';
            }
        }

        const detector = new TestChineseDetector();
        let logCount = 0;

        function log(message, type = 'info') {
            logCount++;
            const timestamp = new Date().toLocaleTimeString();
            const logOutput = document.getElementById('logOutput');
            const prefix = type === 'error' ? '❌' : type === 'success' ? '✅' : 'ℹ️';
            logOutput.textContent += `[${timestamp}] ${prefix} ${message}\n`;
            logOutput.scrollTop = logOutput.scrollHeight;
        }

        function updateLanguageDisplay(selectedId) {
            // 重置所有语言项
            document.querySelectorAll('.language-item').forEach(item => {
                item.className = 'language-item unselected';
                const indicator = item.querySelector('.status-indicator');
                indicator.className = 'status-indicator status-unselected';
            });

            // 高亮选中的语言
            if (selectedId) {
                const selectedItem = document.getElementById(`lang-${selectedId}`);
                if (selectedItem) {
                    selectedItem.className = 'language-item selected';
                    const indicator = selectedItem.querySelector('.status-indicator');
                    indicator.className = 'status-indicator status-selected';
                }
            }
        }

        function updateDetectionResult(result) {
            document.getElementById('detectionStatus').textContent = 
                result.hasChinese ? '检测到中文字符' : '未检测到中文字符';
            document.getElementById('recommendedLanguage').textContent = result.languageName;
            document.getElementById('confidence').textContent = `${result.confidence.toFixed(1)}%`;
        }

        function processInput() {
            const input = document.getElementById('testInput');
            const text = input.value;
            
            if (!text.trim()) {
                updateLanguageDisplay(null);
                updateDetectionResult({ hasChinese: false, languageName: '未检测', confidence: 0 });
                log('输入为空，清除语言选择');
                return;
            }

            const result = detector.detectLanguage(text);
            
            // 更新显示
            updateLanguageDisplay(result.recommendedLanguageId);
            updateDetectionResult(result);
            
            // 记录日志
            log(`检测完成: ${result.hasChinese ? '中文' : '英文'} (ID: ${result.recommendedLanguageId}), 置信度: ${result.confidence.toFixed(1)}%`, 'success');
        }

        function testChineseText() {
            const chineseText = `团号：EJBTBY250715
2PAX
15/7 KLIA2 IN 2015 (AK181) - MOXY PUTRAJAYA
客人：顾婉婷 & 苟晓琼
客人联系：13884407028`;
            
            document.getElementById('testInput').value = chineseText;
            processInput();
            log('载入中文测试内容');
        }

        function testEnglishText() {
            const englishText = `Group: EJBTBY250715
2PAX
15/7 KLIA2 IN 2015 (AK181) - MOXY PUTRAJAYA
Guest: Wang Li & Gou Xiaoqiong
Contact: 13884407028`;
            
            document.getElementById('testInput').value = englishText;
            processInput();
            log('载入英文测试内容');
        }

        function testMixedText() {
            const mixedText = `团号：EJBTBY250715 Group ID
2PAX from KLIA2 到 MOXY PUTRAJAYA
客人：顾婉婷 Guest: Wang Li
Contact: 13884407028`;
            
            document.getElementById('testInput').value = mixedText;
            processInput();
            log('载入混合语言测试内容');
        }

        function clearInput() {
            document.getElementById('testInput').value = '';
            updateLanguageDisplay(null);
            updateDetectionResult({ hasChinese: false, languageName: '未检测', confidence: 0 });
            log('清空输入内容');
        }

        function clearLog() {
            document.getElementById('logOutput').textContent = '';
            logCount = 0;
            log('日志已清除');
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            log('语言选择测试系统初始化完成');
            
            // 绑定输入事件
            document.getElementById('testInput').addEventListener('input', processInput);
            
            // 初始处理
            processInput();
        });
    </script>
</body>
</html>