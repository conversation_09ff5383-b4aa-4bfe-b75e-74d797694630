<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>中文检测功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            line-height: 1.6;
        }
        .test-case {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-input {
            width: 100%;
            height: 100px;
            margin: 10px 0;
            padding: 10px;
            border: 1px solid #ccc;
            border-radius: 3px;
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            background-color: #f5f5f5;
            border-radius: 3px;
        }
        .chinese { background-color: #e8f5e8; }
        .english { background-color: #e8e8f5; }
    </style>
</head>
<body>
    <h1>中文检测功能测试</h1>
    
    <div class="test-case">
        <h3>测试案例 1：纯中文内容</h3>
        <textarea class="test-input" placeholder="输入中文订单信息...">团号：EJBTBY250715
2PAX
15/7 KLIA2 IN 2015 (AK181) - MOXY PUTRAJAYA
客人：顾婉婷 & 苟晓琼
客人联系：13884407028</textarea>
        <div class="result" id="result1">等待检测...</div>
    </div>

    <div class="test-case">
        <h3>测试案例 2：纯英文内容</h3>
        <textarea class="test-input" placeholder="Enter English order information...">Group: EJBTBY250715
2PAX
15/7 KLIA2 IN 2015 (AK181) - MOXY PUTRAJAYA
Guest: Wang Li & Gou Xiaoqiong
Contact: 13884407028</textarea>
        <div class="result" id="result2">等待检测...</div>
    </div>

    <div class="test-case">
        <h3>测试案例 3：中英文混合</h3>
        <textarea class="test-input" placeholder="Enter mixed content...">团号：EJBTBY250715
2PAX from KLIA2 to MOXY PUTRAJAYA
客人：顾婉婷 & 苟晓琼
Contact: 13884407028</textarea>
        <div class="result" id="result3">等待检测...</div>
    </div>

    <script>
        // 模拟中文检测函数
        function detectChinese(text) {
            return /[\u4e00-\u9fff]/.test(text);
        }

        // 测试函数
        function testChineseDetection() {
            const testCases = [
                { id: 'result1', input: document.querySelectorAll('.test-input')[0] },
                { id: 'result2', input: document.querySelectorAll('.test-input')[1] },
                { id: 'result3', input: document.querySelectorAll('.test-input')[2] }
            ];

            testCases.forEach(testCase => {
                const text = testCase.input.value;
                const hasChinese = detectChinese(text);
                const resultElement = document.getElementById(testCase.id);
                
                if (hasChinese) {
                    resultElement.textContent = `✅ 检测到中文字符 → 自动选择中文语言`;
                    resultElement.className = 'result chinese';
                } else {
                    resultElement.textContent = `🔤 未检测到中文字符 → 默认选择英文语言`;
                    resultElement.className = 'result english';
                }
            });
        }

        // 绑定输入事件
        document.querySelectorAll('.test-input').forEach(input => {
            input.addEventListener('input', testChineseDetection);
        });

        // 初始测试
        testChineseDetection();
    </script>
</body>
</html>