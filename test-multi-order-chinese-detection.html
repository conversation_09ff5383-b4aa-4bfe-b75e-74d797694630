<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>多订单中文检测功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            line-height: 1.6;
        }
        .test-section {
            margin: 30px 0;
            padding: 20px;
            border: 2px solid #ddd;
            border-radius: 8px;
            background-color: #f9f9f9;
        }
        .test-case {
            margin: 15px 0;
            padding: 15px;
            border: 1px solid #ccc;
            border-radius: 5px;
            background-color: white;
        }
        .order-input {
            width: 100%;
            height: 80px;
            margin: 10px 0;
            padding: 10px;
            border: 1px solid #ccc;
            border-radius: 3px;
            font-family: monospace;
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 3px;
            font-weight: bold;
        }
        .chinese { 
            background-color: #e8f5e8; 
            color: #2d5a2d;
        }
        .english { 
            background-color: #e8e8f5; 
            color: #2d2d5a;
        }
        .mixed { 
            background-color: #f5f5e8; 
            color: #5a5a2d;
        }
        .test-button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px 5px;
        }
        .test-button:hover {
            background-color: #0056b3;
        }
        .results-container {
            margin-top: 20px;
            padding: 15px;
            background-color: #f0f0f0;
            border-radius: 5px;
        }
        .order-result {
            margin: 8px 0;
            padding: 8px;
            border-radius: 3px;
            border-left: 4px solid #007bff;
            background-color: white;
        }
    </style>
</head>
<body>
    <h1>多订单中文检测功能测试</h1>
    <p>测试多订单模组中的中文自动检测功能：默认选择英文（ID: 2），检测到中文时自动选择中文（ID: 4）</p>
    
    <div class="test-section">
        <h2>测试场景 1：纯中文多订单</h2>
        <div class="test-case">
            <h4>订单 1：中文接机</h4>
            <textarea class="order-input" data-order="1">团号：EJBTBY250715
2PAX
15/7 KLIA2 IN 2015 (AK181) - MOXY PUTRAJAYA
客人：顾婉婷 & 苟晓琼
客人联系：13884407028</textarea>
            <div class="result" id="result1">等待检测...</div>
        </div>
        
        <div class="test-case">
            <h4>订单 2：中文送机</h4>
            <textarea class="order-input" data-order="2">团号：CHINA001
1PAX
17/7 上午10点从双子塔酒店到KLIA
客人：李小明
客人联系：15912345678
备注：需要中文司机</textarea>
            <div class="result" id="result2">等待检测...</div>
        </div>
    </div>

    <div class="test-section">
        <h2>测试场景 2：纯英文多订单</h2>
        <div class="test-case">
            <h4>订单 1：英文接机</h4>
            <textarea class="order-input" data-order="3">Group: EJBTBY250715
2PAX
15/7 KLIA2 IN 2015 (AK181) - MOXY PUTRAJAYA
Guest: Wang Li & Gou Xiaoqiong
Contact: 13884407028</textarea>
            <div class="result" id="result3">等待检测...</div>
        </div>
        
        <div class="test-case">
            <h4>订单 2：英文送机</h4>
            <textarea class="order-input" data-order="4">Group: CHINA001
1PAX
17/7 10AM from Twin Towers Hotel to KLIA
Guest: Li Xiaoming
Contact: 15912345678
Note: English speaking driver preferred</textarea>
            <div class="result" id="result4">等待检测...</div>
        </div>
    </div>

    <div class="test-section">
        <h2>测试场景 3：中英文混合多订单</h2>
        <div class="test-case">
            <h4>订单 1：中英文混合</h4>
            <textarea class="order-input" data-order="5">团号：EJBTBY250715 Group ID
2PAX from KLIA2 到 MOXY PUTRAJAYA
客人：顾婉婷 Guest: Wang Li
Contact: 13884407028</textarea>
            <div class="result" id="result5">等待检测...</div>
        </div>
        
        <div class="test-case">
            <h4>订单 2：纯英文</h4>
            <textarea class="order-input" data-order="6">Transfer service
1PAX
Airport to hotel
Guest: John Smith
Contact: +60123456789</textarea>
            <div class="result" id="result6">等待检测...</div>
        </div>
    </div>

    <div class="test-section">
        <button class="test-button" onclick="runAllTests()">🧪 运行所有测试</button>
        <button class="test-button" onclick="runSingleTest()">🔍 单独测试</button>
        <button class="test-button" onclick="clearResults()">🗑️ 清除结果</button>
        
        <div class="results-container" id="resultsContainer">
            <h3>测试结果</h3>
            <div id="testResults">点击按钮开始测试...</div>
        </div>
    </div>

    <script>
        // 模拟多订单管理器的中文检测功能
        class MultiOrderChineseDetector {
            constructor() {
                this.chineseRegex = /[\u4e00-\u9fff]/;
            }

            processChineseLanguageDetection(orders) {
                console.log('🔍 开始中文语言自动检测...', orders);
                
                return orders.map((order, index) => {
                    const orderText = order.rawText || order.customerName || '';
                    const hasChinese = this.chineseRegex.test(orderText);
                    
                    const processedOrder = { ...order };
                    
                    if (hasChinese) {
                        processedOrder.languagesIdArray = [4]; // 中文
                        console.log(`✅ 订单${index + 1}检测到中文字符，自动选择中文语言`);
                    } else {
                        if (!processedOrder.languagesIdArray || processedOrder.languagesIdArray.length === 0) {
                            processedOrder.languagesIdArray = [2]; // 英文
                            console.log(`🔤 订单${index + 1}未检测到中文字符，默认选择英文语言`);
                        }
                    }
                    
                    return processedOrder;
                });
            }

            detectLanguage(text) {
                const hasChinese = this.chineseRegex.test(text);
                return {
                    hasChinese: hasChinese,
                    recommendedLanguageId: hasChinese ? 4 : 2,
                    languageName: hasChinese ? '中文 (Chinese)' : '英文 (English)'
                };
            }
        }

        const detector = new MultiOrderChineseDetector();

        function runAllTests() {
            const testInputs = document.querySelectorAll('.order-input');
            const orders = [];
            
            // 收集所有订单数据
            testInputs.forEach((input, index) => {
                const orderData = {
                    rawText: input.value,
                    customerName: extractCustomerName(input.value),
                    orderIndex: index + 1
                };
                orders.push(orderData);
            });

            // 处理中文检测
            const processedOrders = detector.processChineseLanguageDetection(orders);
            
            // 显示结果
            displayResults(processedOrders);
            
            // 更新单个结果显示
            processedOrders.forEach((order, index) => {
                updateSingleResult(index + 1, order);
            });
        }

        function runSingleTest() {
            const testInputs = document.querySelectorAll('.order-input');
            
            testInputs.forEach((input, index) => {
                const text = input.value;
                const detection = detector.detectLanguage(text);
                updateSingleResult(index + 1, {
                    rawText: text,
                    languagesIdArray: [detection.recommendedLanguageId],
                    detection: detection
                });
            });
        }

        function updateSingleResult(orderNum, orderData) {
            const resultElement = document.getElementById(`result${orderNum}`);
            const detection = orderData.detection || detector.detectLanguage(orderData.rawText);
            
            if (detection.hasChinese) {
                resultElement.textContent = `✅ 检测到中文字符 → 自动选择中文语言 (ID: 4)`;
                resultElement.className = 'result chinese';
            } else {
                resultElement.textContent = `🔤 未检测到中文字符 → 默认选择英文语言 (ID: 2)`;
                resultElement.className = 'result english';
            }
        }

        function displayResults(processedOrders) {
            const resultsContainer = document.getElementById('testResults');
            let resultsHTML = '<h4>批量处理结果：</h4>';
            
            processedOrders.forEach((order, index) => {
                const detection = detector.detectLanguage(order.rawText);
                const languageId = order.languagesIdArray[0];
                const languageName = languageId === 4 ? '中文' : '英文';
                const statusClass = languageId === 4 ? 'chinese' : 'english';
                
                resultsHTML += `
                    <div class="order-result ${statusClass}">
                        <strong>订单 ${index + 1}:</strong> 
                        ${detection.hasChinese ? '检测到中文' : '未检测到中文'} → 
                        选择${languageName}语言 (ID: ${languageId})
                        <br>
                        <small>文本片段: ${order.rawText.substring(0, 50)}...</small>
                    </div>
                `;
            });
            
            resultsContainer.innerHTML = resultsHTML;
        }

        function extractCustomerName(text) {
            const patterns = [
                /客人[：:]\s*([^\n\r]+)/,
                /Guest[：:]\s*([^\n\r]+)/,
                /姓名[：:]\s*([^\n\r]+)/
            ];
            
            for (const pattern of patterns) {
                const match = text.match(pattern);
                if (match) return match[1].trim();
            }
            return '';
        }

        function clearResults() {
            document.querySelectorAll('.result').forEach(element => {
                element.textContent = '等待检测...';
                element.className = 'result';
            });
            document.getElementById('testResults').textContent = '点击按钮开始测试...';
        }

        // 绑定输入事件进行实时检测
        document.querySelectorAll('.order-input').forEach((input, index) => {
            input.addEventListener('input', () => {
                const detection = detector.detectLanguage(input.value);
                updateSingleResult(index + 1, {
                    rawText: input.value,
                    detection: detection
                });
            });
        });

        // 初始检测
        runSingleTest();
    </script>
</body>
</html>